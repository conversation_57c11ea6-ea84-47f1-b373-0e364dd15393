#!/usr/bin/env python3
"""
Adobe Hackathon 2025 - Round 1B
Persona-Driven Document Intelligence Solution

Processes multiple PDF documents and extracts relevant content based on
specific persona and job-to-be-done requirements.
"""

import os
import json
import fitz  # PyMuPDF
from collections import Counter, defaultdict
import re
from datetime import datetime
from typing import List, Dict, Any, Tuple
import math


def load_input_config():
    """Load the input configuration from JSON file."""
    # Try Docker path first, then local path
    possible_paths = [
        "/app/input/challenge1b_input.json",
        "input/challenge1b_input.json",
        "/app/input",
        "input"
    ]

    config_path = None

    for path in possible_paths:
        if os.path.exists(path):
            if path.endswith('.json'):
                config_path = path
                break
            else:
                # It's a directory, look for JSON files
                json_files = [f for f in os.listdir(path) if f.endswith('.json')]
                if json_files:
                    config_path = os.path.join(path, json_files[0])
                    break

    if not config_path:
        raise FileNotFoundError("No input configuration JSON file found")

    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def extract_keywords_from_persona_job(persona: str, job_to_be_done: str) -> List[str]:
    """Extract relevant keywords from persona and job description with enhanced matching."""
    text = f"{persona} {job_to_be_done}".lower()

    # Remove common stop words
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    }

    # Extract meaningful words (3+ characters, not stop words)
    words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
    keywords = [word for word in words if word not in stop_words]

    # Enhanced domain-specific keywords with synonyms
    domain_keywords = {
        'travel': ['trip', 'vacation', 'hotel', 'restaurant', 'tourist', 'guide', 'city', 'beach', 'culture',
                  'journey', 'destination', 'accommodation', 'sightseeing', 'attraction', 'itinerary',
                  'adventure', 'explore', 'visit', 'tour', 'leisure', 'holiday'],
        'food': ['recipe', 'cooking', 'ingredient', 'meal', 'dish', 'cuisine', 'vegetarian', 'menu',
                'culinary', 'chef', 'kitchen', 'preparation', 'flavor', 'taste', 'nutrition',
                'buffet', 'catering', 'dining', 'feast', 'banquet', 'appetizer', 'dessert'],
        'hr': ['employee', 'form', 'onboarding', 'compliance', 'document', 'process', 'workflow',
               'human resources', 'personnel', 'staff', 'recruitment', 'hiring', 'training',
               'policy', 'procedure', 'management', 'organization'],
        'student': ['study', 'learn', 'exam', 'course', 'education', 'academic', 'knowledge',
                   'university', 'college', 'school', 'curriculum', 'textbook', 'lecture',
                   'assignment', 'research', 'degree', 'scholarship'],
        'business': ['analysis', 'report', 'financial', 'revenue', 'market', 'strategy', 'investment',
                    'corporate', 'company', 'profit', 'growth', 'performance', 'management',
                    'planning', 'development', 'operations', 'sales']
    }

    # Add domain-specific keywords with fuzzy matching
    for domain, domain_words in domain_keywords.items():
        if any(domain_word in text for domain_word in [domain] + domain_words[:3]):
            keywords.extend(domain_words)

    # Add specific job-related keywords
    job_specific_keywords = {
        'planner': ['plan', 'planning', 'schedule', 'organize', 'coordinate', 'arrange'],
        'contractor': ['contract', 'service', 'provider', 'supplier', 'vendor', 'professional'],
        'professional': ['expert', 'specialist', 'experienced', 'skilled', 'qualified'],
        'friends': ['group', 'social', 'together', 'collective', 'shared', 'community'],
        'college': ['young', 'student', 'university', 'campus', 'academic', 'education']
    }

    for job_type, job_words in job_specific_keywords.items():
        if job_type in text:
            keywords.extend(job_words)

    return list(set(keywords))  # Remove duplicates


def clean_text(text):
    """Clean and normalize text content."""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Be more lenient with short text - headings can be short
    if len(text) < 2:
        return ""
    
    return text


def extract_document_content(pdf_path: str) -> Dict[str, Any]:
    """Extract content from a single PDF document."""
    doc = fitz.open(pdf_path)

    # Extract text blocks with metadata
    text_blocks = []
    all_text = ""
    total_pages = len(doc)  # Get page count before processing

    for page_num in range(total_pages):
        page = doc[page_num]
        blocks = page.get_text("dict")

        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = clean_text(span["text"])
                        if text:
                            font_size = round(span["size"], 1)
                            text_blocks.append({
                                "text": text,
                                "font_size": font_size,
                                "page": page_num + 1,
                                "flags": span["flags"]
                            })
                            all_text += " " + text

    doc.close()

    return {
        "text_blocks": text_blocks,
        "full_text": all_text.strip(),
        "total_pages": total_pages
    }


def score_section_relevance(section_text: str, keywords: List[str]) -> float:
    """Score how relevant a section is to the persona and job with enhanced algorithm."""
    if not section_text or not keywords:
        return 0.0

    text_lower = section_text.lower()
    text_length = len(section_text)

    if text_length == 0:
        return 0.0

    # Count exact keyword matches
    exact_matches = 0
    total_keyword_chars = 0
    matched_keywords = set()

    for keyword in keywords:
        keyword_lower = keyword.lower()
        if keyword_lower in text_lower:
            count = text_lower.count(keyword_lower)
            exact_matches += count
            total_keyword_chars += len(keyword_lower) * count
            matched_keywords.add(keyword_lower)

    # Count partial matches (substring matching)
    partial_matches = 0
    for keyword in keywords:
        keyword_lower = keyword.lower()
        if len(keyword_lower) >= 4:  # Only for longer keywords
            for word in re.findall(r'\b\w+\b', text_lower):
                if keyword_lower in word or word in keyword_lower:
                    if keyword_lower not in matched_keywords:  # Avoid double counting
                        partial_matches += 0.5

    # Calculate base scores
    keyword_density = total_keyword_chars / text_length
    keyword_frequency = exact_matches / len(keywords) if keywords else 0

    # Diversity bonus - reward sections that match multiple different keywords
    unique_keywords_found = len(matched_keywords)
    keyword_diversity_bonus = unique_keywords_found / len(keywords) if keywords else 0

    # Length bonus - prefer sections that are substantial but not too long
    optimal_length = 100  # Optimal section length
    length_factor = min(1.0, text_length / optimal_length) * (1.0 - min(0.5, text_length / 500))

    # Position bonus - sections with certain patterns are more likely to be important
    position_bonus = 0.0
    if any(pattern in text_lower for pattern in ['introduction', 'overview', 'summary', 'conclusion']):
        position_bonus = 0.1

    # Combine scores with weights
    relevance_score = (
        keyword_density * 8.0 +           # Primary factor
        keyword_frequency * 2.0 +         # Frequency of matches
        keyword_diversity_bonus * 3.0 +   # Diversity of keywords
        partial_matches * 0.5 +           # Partial matches
        length_factor * 1.0 +             # Optimal length
        position_bonus                    # Position indicators
    )

    # Normalize to 0-1 range
    relevance_score = min(1.0, relevance_score / 10.0)

    return relevance_score


def extract_sections_from_document(content: Dict[str, Any], filename: str, keywords: List[str]) -> List[Dict[str, Any]]:
    """Extract and score sections from document content."""
    text_blocks = content["text_blocks"]
    
    if not text_blocks:
        return []
    
    # Use similar logic to Round 1A for finding headings
    font_sizes = [block["font_size"] for block in text_blocks]
    font_counter = Counter(font_sizes)
    unique_sizes = sorted(set(font_sizes), reverse=True)
    
    # Identify potential heading sizes
    heading_sizes = []
    total_blocks = len(text_blocks)
    
    for size in unique_sizes:
        frequency = font_counter[size]
        percentage = frequency / total_blocks
        
        # Include sizes that appear less frequently (likely headings)
        if percentage < 0.3:
            heading_sizes.append(size)
    
    # Extract sections
    sections = []
    
    for block in text_blocks:
        if block["font_size"] in heading_sizes:
            text = block["text"]
            
            # Score relevance to persona/job
            relevance_score = score_section_relevance(text, keywords)
            
            if relevance_score > 0.1:  # Only include somewhat relevant sections
                sections.append({
                    "document": filename,
                    "section_title": text,
                    "page_number": block["page"],
                    "relevance_score": relevance_score,
                    "font_size": block["font_size"]
                })
    
    return sections


def rank_sections_by_importance(sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Rank sections by importance for the given persona and job."""
    if not sections:
        return []
    
    # Sort by relevance score (descending) and font size (descending)
    sections.sort(key=lambda x: (x["relevance_score"], x["font_size"]), reverse=True)
    
    # Add importance rank
    for i, section in enumerate(sections):
        section["importance_rank"] = i + 1
    
    return sections


def extract_subsection_analysis(content: Dict[str, Any], top_sections: List[Dict[str, Any]], 
                               keywords: List[str], max_subsections: int = 5) -> List[Dict[str, Any]]:
    """Extract refined text for subsection analysis."""
    if not top_sections:
        return []
    
    full_text = content["full_text"]
    text_blocks = content["text_blocks"]
    
    subsections = []
    
    # For each top section, find related content
    for section in top_sections[:max_subsections]:
        section_title = section["section_title"]
        page_num = section["page_number"]
        
        # Find text blocks near this section
        relevant_blocks = []
        
        for block in text_blocks:
            # Include blocks from same page or adjacent pages
            if abs(block["page"] - page_num) <= 1:
                block_relevance = score_section_relevance(block["text"], keywords)
                if block_relevance > 0.05:  # Lower threshold for subsections
                    relevant_blocks.append(block)
        
        # Combine relevant blocks into refined text
        if relevant_blocks:
            # Sort by page and position (approximate)
            relevant_blocks.sort(key=lambda x: (x["page"], x["font_size"]), reverse=True)
            
            # Take the most relevant text (limit length)
            refined_texts = []
            total_length = 0
            max_length = 500  # Limit refined text length
            
            for block in relevant_blocks:
                if total_length + len(block["text"]) <= max_length:
                    refined_texts.append(block["text"])
                    total_length += len(block["text"])
                else:
                    break
            
            if refined_texts:
                refined_text = " ".join(refined_texts)
                
                subsections.append({
                    "document": section["document"],
                    "refined_text": refined_text,
                    "page_number": page_num
                })
    
    return subsections


def process_documents():
    """Main processing function for Round 1B."""
    try:
        # Load input configuration
        config = load_input_config()
        
        # Extract configuration
        challenge_info = config.get("challenge_info", {})
        documents = config.get("documents", [])
        persona = config.get("persona", {}).get("role", "")
        job_to_be_done = config.get("job_to_be_done", {}).get("task", "")
        
        # Extract keywords from persona and job
        keywords = extract_keywords_from_persona_job(persona, job_to_be_done)
        
        print(f"Processing {len(documents)} documents for persona: {persona}")
        print(f"Job: {job_to_be_done}")
        print(f"Keywords: {keywords}")
        
        # Process each document
        all_sections = []
        # Try Docker path first, then local path
        input_dir = "/app/input" if os.path.exists("/app/input") else "input"
        
        for doc_info in documents:
            filename = doc_info["filename"]
            pdf_path = os.path.join(input_dir, filename)
            
            if os.path.exists(pdf_path):
                print(f"Processing: {filename}")
                
                # Extract content
                content = extract_document_content(pdf_path)
                
                # Extract sections
                sections = extract_sections_from_document(content, filename, keywords)
                all_sections.extend(sections)
                
                # Store content for later subsection analysis
                doc_info["content"] = content
            else:
                print(f"Warning: {filename} not found")
        
        # Rank all sections by importance
        ranked_sections = rank_sections_by_importance(all_sections)
        
        # Take top sections for output
        top_sections = ranked_sections[:10]  # Limit to top 10
        
        # Generate subsection analysis
        subsection_analysis = []
        for doc_info in documents:
            if "content" in doc_info:
                doc_sections = [s for s in top_sections if s["document"] == doc_info["filename"]]
                if doc_sections:
                    doc_subsections = extract_subsection_analysis(
                        doc_info["content"], doc_sections, keywords, max_subsections=3
                    )
                    subsection_analysis.extend(doc_subsections)
        
        # Generate output
        output = {
            "metadata": {
                "input_documents": [doc["filename"] for doc in documents],
                "persona": persona,
                "job_to_be_done": job_to_be_done,
                "processing_timestamp": datetime.now().isoformat()
            },
            "extracted_sections": [
                {
                    "document": section["document"],
                    "section_title": section["section_title"],
                    "importance_rank": section["importance_rank"],
                    "page_number": section["page_number"]
                }
                for section in top_sections
            ],
            "subsection_analysis": subsection_analysis
        }
        
        # Save output
        # Try Docker path first, then local path
        output_dir = "/app/output" if os.path.exists("/app") else "output"
        os.makedirs(output_dir, exist_ok=True)
        
        challenge_id = challenge_info.get("challenge_id", "round_1b_output")
        output_path = os.path.join(output_dir, f"{challenge_id}.json")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
        
        print(f"Output saved to: {output_path}")
        print(f"Processed {len(all_sections)} total sections")
        print(f"Top {len(top_sections)} sections selected")
        print(f"Generated {len(subsection_analysis)} subsection analyses")
        
    except Exception as e:
        print(f"Error processing documents: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    process_documents()
