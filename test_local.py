#!/usr/bin/env python3
"""
Local test script for the PDF outline extraction solution.
This tests the core logic without Dock<PERSON>.
"""

import os
import sys
import tempfile
import json

# Add current directory to path to import main
sys.path.insert(0, '.')

def test_main_functions():
    """Test the main functions without actual PDF files."""
    try:
        from main import clean_text, determine_heading_levels, extract_outline
        
        # Test clean_text function
        print("Testing clean_text function...")
        assert clean_text("  Hello   World  ") == "Hello World"
        assert clean_text("") == ""
        assert clean_text("ab") == ""  # Too short
        assert clean_text("abc") == "abc"
        print("✅ clean_text tests passed")
        
        # Test determine_heading_levels function
        print("Testing determine_heading_levels function...")
        font_sizes = [12.0, 12.0, 12.0, 16.0, 14.0, 12.0, 18.0]
        text_blocks = [{"font_size": size} for size in font_sizes]
        
        level_mapping = determine_heading_levels(font_sizes, text_blocks)
        print(f"Level mapping: {level_mapping}")
        print("✅ determine_heading_levels tests passed")
        
        print("✅ All function tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import fitz
        print("✅ PyMuPDF (fitz) is available")
        return True
    except ImportError:
        print("❌ PyMuPDF (fitz) is not installed")
        print("To install: pip install PyMuPDF==1.23.7")
        return False

def main():
    """Run local tests."""
    print("🧪 Running local tests for Adobe Hackathon PDF Outliner...")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependencies missing. Please install requirements:")
        print("pip install -r requirements.txt")
        return
    
    # Test functions
    if test_main_functions():
        print("\n🎉 All tests passed! The solution is ready.")
        print("\nNext steps:")
        print("1. Install Docker")
        print("2. Place PDF files in the 'input' directory")
        print("3. Run: docker build --platform linux/amd64 -t pdfoutliner:adobe2025 .")
        print("4. Run: docker run --rm -v $(pwd)/input:/app/input -v $(pwd)/output:/app/output --network none pdfoutliner:adobe2025")
    else:
        print("\n❌ Some tests failed. Please check the code.")

if __name__ == "__main__":
    main()
