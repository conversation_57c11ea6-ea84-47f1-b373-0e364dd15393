FROM --platform=linux/amd64 python:3.10-slim

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy Round 1B application code
COPY main_1b.py .

# Create input and output directories
RUN mkdir -p /app/input /app/output

# Set the entrypoint to Round 1B processor
ENTRYPOINT ["python", "main_1b.py"]
