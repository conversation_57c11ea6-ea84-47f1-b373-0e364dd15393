# Adobe Hackathon 2025 – Round 1A: PDF Outline Extraction

## 🔍 Objective
Extract structured outline from PDF files – including Title, H1, H2, H3 – and output it in JSON format.

**Note**: This repository contains only Round 1A solution. Round 1B (Persona-Driven Document Intelligence) is maintained in a separate repository.

## 🧠 Approach
- Used PyMuPDF to extract text and font sizes from PDF documents
- Inferred heading hierarchy based on font size distribution and frequency analysis
- Implemented smart filtering to distinguish headings from body text
- Output structured data with page numbers in valid JSON format

## 🧰 Tech Stack
- **Python 3.10** - Core programming language
- **PyMuPDF (fitz)** - PDF parsing and text extraction
- **Docker** - For platform compatibility and offline execution

## 🏗️ Project Structure
```
project/
├── main.py              # Core PDF outline extraction logic
├── Dockerfile           # Container configuration for AMD64 platform
├── requirements.txt     # Python dependencies
├── input/               # Input PDF files (volume-mounted)
├── output/              # Output JSON files (volume-mounted)
└── README.md            # This documentation
```

## 🚀 Quick Start

### Prerequisites
- Docker installed on your system
- Input PDF files (max 50 pages each)

### Build and Run
```bash
# Build the Docker image
docker build --platform linux/amd64 -t pdfoutliner:adobe2025 .

# Run the container
docker run --rm \
  -v $(pwd)/input:/app/input \
  -v $(pwd)/output:/app/output \
  --network none \
  pdfoutliner:adobe2025
```

### For Windows PowerShell:
```powershell
# Build the Docker image
docker build --platform linux/amd64 -t pdfoutliner:adobe2025 .

# Run the container
docker run --rm -v ${PWD}/input:/app/input -v ${PWD}/output:/app/output --network none pdfoutliner:adobe2025
```

## 📁 Usage

1. **Place PDF files** in the `input/` directory
2. **Run the Docker container** using the commands above
3. **Check the `output/` directory** for generated JSON files

Each input PDF will generate a corresponding JSON file with the same base name.

## 🧾 Output Format

```json
{
  "title": "Understanding Artificial Intelligence",
  "outline": [
    { "level": "H1", "text": "Introduction to AI", "page": 1 },
    { "level": "H2", "text": "What is Machine Learning?", "page": 2 },
    { "level": "H3", "text": "Supervised Learning", "page": 3 },
    { "level": "H3", "text": "Unsupervised Learning", "page": 4 },
    { "level": "H2", "text": "Deep Learning Fundamentals", "page": 5 }
  ]
}
```

## 🔧 Algorithm Details

### Font-Based Heuristics
1. **Extract all text spans** with font size information using PyMuPDF
2. **Analyze font size distribution** across the document
3. **Filter out body text** by identifying frequently used font sizes (>30% frequency)
4. **Map remaining font sizes** to heading levels:
   - Largest font → Title
   - Next largest → H1
   - Next → H2
   - Next → H3

### Text Processing
- Clean and normalize whitespace
- Filter out very short text snippets (< 3 characters)
- Preserve page number information for each heading

## ⚡ Performance Specifications

- **Runtime**: ≤ 10 seconds for 50-page PDFs
- **Platform**: AMD64 (x86_64) compatible
- **Network**: Fully offline operation
- **Memory**: Optimized for minimal resource usage

## 🧪 Testing

To test with sample files:
1. Download sample PDFs from the official repository
2. Place them in the `input/` directory
3. Run the Docker container
4. Verify JSON output matches expected format

## 🏆 Hackathon Compliance

✅ **Heading Detection**: Accurate extraction of Title, H1, H2, H3  
✅ **Performance**: Fast processing under time constraints  
✅ **Platform**: AMD64 Docker compatibility  
✅ **Offline**: No external network dependencies  
✅ **Format**: Valid JSON output with required structure  

## 📝 Notes

- The solution uses dynamic font analysis rather than hardcoded rules
- Handles various PDF layouts and font configurations
- Gracefully handles edge cases and malformed PDFs
- Optimized for the hackathon's specific requirements and constraints

---

**Built for Adobe India Hackathon 2025 - Round 1A**
