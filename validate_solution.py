#!/usr/bin/env python3
"""
Validation script for Adobe Hackathon PDF Outliner solution.
Checks code structure and files without requiring dependencies.
"""

import os
import json
import ast

def check_file_structure():
    """Check if all required files exist."""
    required_files = [
        'main.py',
        'Dockerfile', 
        'requirements.txt',
        'README.md'
    ]
    
    required_dirs = [
        'input',
        'output'
    ]
    
    print("📁 Checking file structure...")
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    missing_dirs = []
    for dir in required_dirs:
        if os.path.exists(dir) and os.path.isdir(dir):
            print(f"✅ {dir}/")
        else:
            print(f"❌ {dir}/")
            missing_dirs.append(dir)
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def validate_main_py():
    """Validate main.py structure and syntax."""
    print("\n🐍 Validating main.py...")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check syntax
        ast.parse(content)
        print("✅ Python syntax is valid")
        
        # Check for required functions
        required_functions = [
            'clean_text',
            'extract_font_info', 
            'determine_heading_levels',
            'extract_outline',
            'process_pdfs'
        ]
        
        for func in required_functions:
            if f"def {func}" in content:
                print(f"✅ Function {func} found")
            else:
                print(f"❌ Function {func} missing")
                return False
        
        # Check for required imports
        required_imports = ['fitz', 'json', 'os']
        for imp in required_imports:
            if f"import {imp}" in content or f"from {imp}" in content:
                print(f"✅ Import {imp} found")
            else:
                print(f"❌ Import {imp} missing")
                return False
        
        return True
        
    except FileNotFoundError:
        print("❌ main.py not found")
        return False
    except SyntaxError as e:
        print(f"❌ Syntax error in main.py: {e}")
        return False

def validate_dockerfile():
    """Validate Dockerfile content."""
    print("\n🐳 Validating Dockerfile...")
    
    try:
        with open('Dockerfile', 'r') as f:
            content = f.read()
        
        required_elements = [
            'FROM --platform=linux/amd64',
            'python:3.10',
            'WORKDIR /app',
            'COPY requirements.txt',
            'pip install',
            'COPY main.py',
            'ENTRYPOINT ["python", "main.py"]'
        ]
        
        for element in required_elements:
            if element in content:
                print(f"✅ {element}")
            else:
                print(f"❌ {element} missing")
                return False
        
        return True
        
    except FileNotFoundError:
        print("❌ Dockerfile not found")
        return False

def validate_requirements():
    """Validate requirements.txt."""
    print("\n📦 Validating requirements.txt...")
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read().strip()
        
        if 'PyMuPDF' in content:
            print("✅ PyMuPDF dependency found")
            return True
        else:
            print("❌ PyMuPDF dependency missing")
            return False
            
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def main():
    """Run all validations."""
    print("🔍 Adobe Hackathon 2025 - Solution Validation")
    print("=" * 50)
    
    checks = [
        check_file_structure(),
        validate_main_py(),
        validate_dockerfile(), 
        validate_requirements()
    ]
    
    if all(checks):
        print("\n🎉 All validations passed!")
        print("\n📋 Solution Summary:")
        print("✅ Complete file structure")
        print("✅ Valid Python code with all required functions")
        print("✅ Proper Dockerfile for AMD64 platform")
        print("✅ Correct dependencies specified")
        print("\n🚀 Ready for Docker build and submission!")
        print("\nNext steps:")
        print("1. Ensure Docker is installed")
        print("2. Place test PDFs in input/ directory")
        print("3. Build: docker build --platform linux/amd64 -t pdfoutliner:adobe2025 .")
        print("4. Run: docker run --rm -v $(pwd)/input:/app/input -v $(pwd)/output:/app/output --network none pdfoutliner:adobe2025")
    else:
        print("\n❌ Some validations failed. Please fix the issues above.")

if __name__ == "__main__":
    main()
