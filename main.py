#!/usr/bin/env python3
"""
Adobe Hackathon 2025 - Round 1A
PDF Outline Extraction Solution

Extracts structured outline from PDF files using font-based heuristics.
"""

import os
import json
import fitz  # PyMuPDF
from collections import Counter
import re


def clean_text(text):
    """Clean and normalize text content."""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove very short text (likely noise)
    if len(text) < 3:
        return ""
    
    return text


def extract_font_info(pdf_path):
    """Extract text with font information from PDF."""
    doc = fitz.open(pdf_path)
    font_sizes = []
    text_blocks = []
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        blocks = page.get_text("dict")
        
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = clean_text(span["text"])
                        if text:
                            font_size = round(span["size"], 1)
                            font_sizes.append(font_size)
                            text_blocks.append({
                                "text": text,
                                "font_size": font_size,
                                "page": page_num + 1,
                                "flags": span["flags"]
                            })
    
    doc.close()
    return text_blocks, font_sizes


def determine_heading_levels(font_sizes, text_blocks):
    """Determine heading levels based on font size frequency and distribution."""
    # Count font size frequencies
    font_counter = Counter(font_sizes)
    
    # Get unique font sizes sorted in descending order
    unique_sizes = sorted(set(font_sizes), reverse=True)
    
    # Filter out very common font sizes (likely body text)
    # Body text typically appears very frequently
    total_blocks = len(text_blocks)
    size_thresholds = {}
    
    for size in unique_sizes:
        frequency = font_counter[size]
        percentage = frequency / total_blocks
        
        # If a font size appears in more than 30% of blocks, it's likely body text
        if percentage > 0.3:
            continue
        
        size_thresholds[size] = frequency
    
    # Sort by font size (descending) and select top levels
    heading_sizes = sorted(size_thresholds.keys(), reverse=True)
    
    # Map font sizes to heading levels
    level_mapping = {}
    
    if len(heading_sizes) >= 1:
        level_mapping[heading_sizes[0]] = "title"
    if len(heading_sizes) >= 2:
        level_mapping[heading_sizes[1]] = "H1"
    if len(heading_sizes) >= 3:
        level_mapping[heading_sizes[2]] = "H2"
    if len(heading_sizes) >= 4:
        level_mapping[heading_sizes[3]] = "H3"
    
    return level_mapping


def extract_outline(pdf_path):
    """Extract structured outline from PDF."""
    text_blocks, font_sizes = extract_font_info(pdf_path)
    
    if not text_blocks:
        return {"title": "", "outline": []}
    
    level_mapping = determine_heading_levels(font_sizes, text_blocks)
    
    title = ""
    outline = []
    
    for block in text_blocks:
        font_size = block["font_size"]
        text = block["text"]
        page = block["page"]
        
        if font_size in level_mapping:
            level = level_mapping[font_size]
            
            if level == "title" and not title:
                title = text
            elif level in ["H1", "H2", "H3"]:
                outline.append({
                    "level": level,
                    "text": text,
                    "page": page
                })
    
    # If no title was found, try to use the first H1 or largest text
    if not title and outline:
        title = outline[0]["text"]
    elif not title and text_blocks:
        # Use the first significant text block as title
        title = text_blocks[0]["text"]
    
    return {
        "title": title,
        "outline": outline
    }


def process_pdfs():
    """Process all PDFs in the input directory."""
    input_dir = "input"
    output_dir = "output"
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each PDF file
    for filename in os.listdir(input_dir):
        if filename.lower().endswith('.pdf'):
            pdf_path = os.path.join(input_dir, filename)
            
            try:
                # Extract outline
                result = extract_outline(pdf_path)
                
                # Generate output filename
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_dir, f"{base_name}.json")
                
                # Save JSON output
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"Processed: {filename} -> {base_name}.json")
                
            except Exception as e:
                print(f"Error processing {filename}: {str(e)}")


if __name__ == "__main__":
    process_pdfs()
