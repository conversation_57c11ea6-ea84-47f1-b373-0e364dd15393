#!/usr/bin/env python3
"""
Adobe Hackathon 2025 - Round 1A
PDF Outline Extraction Solution

Extracts structured outline from PDF files using font-based heuristics.
"""

import os
import json
import fitz  # PyMuPDF
from collections import Counter
import re


def clean_text(text):
    """Clean and normalize text content."""
    if not text:
        return ""

    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())

    # Be more lenient with short text - headings can be short
    # Only remove very short text (< 2 characters)
    if len(text) < 2:
        return ""

    return text


def extract_font_info(pdf_path):
    """Extract text with font information from PDF."""
    doc = fitz.open(pdf_path)
    font_sizes = []
    text_blocks = []
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        blocks = page.get_text("dict")
        
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = clean_text(span["text"])
                        if text:
                            font_size = round(span["size"], 1)
                            font_sizes.append(font_size)
                            text_blocks.append({
                                "text": text,
                                "font_size": font_size,
                                "page": page_num + 1,
                                "flags": span["flags"]
                            })
    
    doc.close()
    return text_blocks, font_sizes


def is_likely_heading(text, font_size, avg_length_for_size, frequency, total_blocks):
    """Determine if text is likely a heading based on multiple criteria."""
    # Length-based criteria
    text_length = len(text)

    # Headings are typically:
    # 1. Short to medium length (5-80 characters)
    # 2. Don't end with periods (usually)
    # 3. May contain colons, numbers, or be all caps
    # 4. Appear less frequently than body text

    if text_length < 5 or text_length > 80:
        return False

    # Check for heading patterns
    heading_indicators = [
        text.endswith(':'),  # "Mission Statement:", "Goals:"
        text.isupper() and len(text) > 5,  # "PATHWAY OPTIONS"
        re.match(r'^[A-Z][a-z]+ [A-Z][a-z]+', text),  # "Course Offerings"
        text.count(' ') <= 4,  # Not too many words
        not text.endswith('.'),  # Headings rarely end with periods
    ]

    # If text has heading indicators and reasonable frequency, likely a heading
    frequency_ratio = frequency / total_blocks
    if any(heading_indicators) and frequency_ratio < 0.3:
        return True

    # Very short text with low frequency is likely a heading
    if text_length <= 20 and frequency_ratio < 0.1:
        return True

    return False


def determine_heading_levels(font_sizes, text_blocks):
    """Determine heading levels based on font size frequency and distribution."""
    # Count font size frequencies
    font_counter = Counter(font_sizes)

    # Get unique font sizes sorted in descending order
    unique_sizes = sorted(set(font_sizes), reverse=True)

    total_blocks = len(text_blocks)

    # Calculate average text length for each font size
    size_text_lengths = {}
    for block in text_blocks:
        size = block["font_size"]
        if size not in size_text_lengths:
            size_text_lengths[size] = []
        size_text_lengths[size].append(len(block["text"]))

    avg_lengths = {}
    for size, lengths in size_text_lengths.items():
        avg_lengths[size] = sum(lengths) / len(lengths) if lengths else 0

    # Filter font sizes for potential headings
    heading_candidates = {}

    for size in unique_sizes:
        frequency = font_counter[size]
        avg_length = avg_lengths.get(size, 0)

        # Get sample texts for this font size to check if they look like headings
        sample_texts = [block["text"] for block in text_blocks if block["font_size"] == size]

        # Check if any text at this font size looks like a heading
        has_heading_like_text = any(
            is_likely_heading(text, size, avg_length, frequency, total_blocks)
            for text in sample_texts
        )

        if has_heading_like_text:
            heading_candidates[size] = frequency

    # Sort by font size (descending)
    heading_sizes = sorted(heading_candidates.keys(), reverse=True)

    # Map font sizes to heading levels
    level_mapping = {}

    # Find the largest font size on page 1 for title
    page_1_blocks = [block for block in text_blocks if block["page"] == 1]
    if page_1_blocks:
        page_1_sizes = [block["font_size"] for block in page_1_blocks]
        title_size = max(page_1_sizes)
        level_mapping[title_size] = "title"

        # Remove title size from heading sizes for other levels
        if title_size in heading_sizes:
            heading_sizes.remove(title_size)

    # Assign remaining sizes to H1, H2, H3 levels
    if len(heading_sizes) >= 1:
        level_mapping[heading_sizes[0]] = "H1"
    if len(heading_sizes) >= 2:
        level_mapping[heading_sizes[1]] = "H2"
    if len(heading_sizes) >= 3:
        level_mapping[heading_sizes[2]] = "H3"
    if len(heading_sizes) >= 4:
        level_mapping[heading_sizes[3]] = "H3"

    return level_mapping


def extract_outline(pdf_path):
    """Extract structured outline from PDF."""
    text_blocks, font_sizes = extract_font_info(pdf_path)

    if not text_blocks:
        return {"title": "", "outline": []}

    level_mapping = determine_heading_levels(font_sizes, text_blocks)

    title = ""
    outline = []
    title_used = False

    # First pass: find title from page 1 (largest font, preferably centered/top)
    page_1_blocks = [block for block in text_blocks if block["page"] == 1]
    if page_1_blocks:
        # Find the largest font size on page 1
        max_size_page_1 = max(block["font_size"] for block in page_1_blocks)

        # Look for title candidates (largest font on page 1)
        for block in page_1_blocks:
            if block["font_size"] == max_size_page_1 and not title:
                title = block["text"]
                title_used = True
                break

    # Second pass: extract headings
    for block in text_blocks:
        font_size = block["font_size"]
        text = block["text"]
        page = block["page"]

        if font_size in level_mapping:
            level = level_mapping[font_size]

            # Skip if this text was already used as title
            if level == "title" and title_used and text == title:
                continue
            elif level == "title" and not title_used:
                title = text
                title_used = True
            elif level in ["H1", "H2", "H3"]:
                # Additional check: only include if it looks like a heading
                frequency = sum(1 for b in text_blocks if b["font_size"] == font_size)
                if is_likely_heading(text, font_size, len(text), frequency, len(text_blocks)):
                    outline.append({
                        "level": level,
                        "text": text,
                        "page": page
                    })

    # Fallback title selection
    if not title and outline:
        title = outline[0]["text"]
        # Remove the first outline item since it's now the title
        outline = outline[1:]
    elif not title and text_blocks:
        # Use the first significant text block as title
        title = text_blocks[0]["text"]

    return {
        "title": title,
        "outline": outline
    }


def process_pdfs():
    """Process all PDFs in the input directory."""
    input_dir = "input"
    output_dir = "output"
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each PDF file
    for filename in os.listdir(input_dir):
        if filename.lower().endswith('.pdf'):
            pdf_path = os.path.join(input_dir, filename)
            
            try:
                # Extract outline
                result = extract_outline(pdf_path)
                
                # Generate output filename
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_dir, f"{base_name}.json")
                
                # Save JSON output
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"Processed: {filename} -> {base_name}.json")
                
            except Exception as e:
                print(f"Error processing {filename}: {str(e)}")


if __name__ == "__main__":
    process_pdfs()
